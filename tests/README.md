# 支付宝数据同步系统 - 测试套件

这是一个完整的测试套件，用于验证支付宝数据同步系统的各项功能是否正常工作。

## 🚀 快速开始

### 运行所有测试
```bash
php run_tests.php all
```

### 快速检查系统状态
```bash
php run_tests.php quick
```

### 交互式测试菜单
```bash
php run_tests.php
```

## 📋 测试模块

### 1. 基础功能测试 (BasicTests)
- **配置文件测试**: 检查数据库配置和必要目录
- **数据库连接测试**: 验证数据库连接和基本查询
- **表结构测试**: 检查数据表是否存在及结构完整性
- **数据操作测试**: 测试增删改查操作
- **系统服务测试**: 验证核心服务功能
- **文件权限测试**: 检查关键目录的读写权限

```bash
php run_tests.php basic
```

### 2. API功能测试 (ApiTests)
- **支付宝配置测试**: 验证API配置完整性
- **BillFetcher初始化测试**: 测试数据获取服务初始化
- **数据验证测试**: 验证数据格式和有效性检查
- **数据去重测试**: 测试重复数据处理机制
- **日期范围处理测试**: 验证日期范围验证逻辑
- **错误处理测试**: 测试异常情况处理
- **日志记录测试**: 验证日志功能

```bash
php run_tests.php api
```

### 3. Web界面测试 (WebTests)
- **Web文件存在性测试**: 检查Web文件是否存在
- **Web文件语法测试**: 验证PHP语法正确性
- **Web页面结构测试**: 检查HTML结构完整性
- **Web数据库连接测试**: 测试Web页面数据库连接
- **Web服务器脚本测试**: 验证启动脚本功能
- **Web安全性测试**: 检查基本安全措施
- **Web响应性测试**: 验证响应式设计
- **Web功能测试**: 检查核心功能完整性

```bash
php run_tests.php web
```

### 4. 性能测试 (PerformanceTests)
- **数据库查询性能测试**: 测试查询响应时间
- **内存使用测试**: 监控内存使用情况
- **批量处理性能测试**: 测试不同批量大小的处理性能
- **并发处理测试**: 验证并发连接处理能力
- **文件I/O性能测试**: 测试文件读写性能
- **系统资源测试**: 检查磁盘空间和系统配置

```bash
php run_tests.php performance
```

## 🛠️ 使用方法

### 命令行参数

| 参数 | 说明 |
|------|------|
| `all` | 运行所有测试 |
| `basic` | 运行基础功能测试 |
| `api` | 运行API功能测试 |
| `web` | 运行Web界面测试 |
| `performance` | 运行性能测试 |
| `quick` | 快速系统检查 |
| `clean` | 清理测试数据 |
| `list` | 列出所有可用测试 |

### 交互式菜单

不带参数运行 `php run_tests.php` 将进入交互式菜单模式，可以选择运行特定的测试模块。

## 📊 测试结果

### 测试输出格式
- ✅ 表示测试通过
- ❌ 表示测试失败
- ⚠️ 表示警告或需要注意的问题
- 💡 表示提示信息

### 测试日志
测试结果会自动保存到 `logs/test_results.log` 文件中，包含：
- 测试时间戳
- 测试通过率
- 详细的测试结果
- 性能数据

### 查看测试历史
```bash
php run_tests.php
# 选择菜单项 7
```

## 🧹 测试数据清理

测试过程中会产生一些测试数据，可以使用以下命令清理：

```bash
php run_tests.php clean
```

这将清理：
- 数据库中的测试记录
- 临时测试日志文件

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `config/database.php` 配置
   - 确认数据库服务运行状态
   - 验证数据库用户权限

2. **文件权限错误**
   - 确保 `logs` 目录可写
   - 检查PHP进程的文件权限

3. **内存不足**
   - 检查PHP内存限制设置
   - 考虑增加 `memory_limit`

4. **测试超时**
   - 检查 `max_execution_time` 设置
   - 优化数据库查询性能

### 调试模式

如果测试失败，可以：
1. 查看详细错误信息
2. 检查相关日志文件
3. 运行单独的测试模块
4. 使用快速检查定位问题

## 📈 性能基准

### 预期性能指标
- 数据库连接: < 100ms
- 简单查询: < 50ms
- 复杂查询: < 500ms
- 批量插入(100条): < 1000ms
- 内存使用: < 64MB

### 性能优化建议
- 添加适当的数据库索引
- 使用批量操作处理大量数据
- 定期清理过期数据
- 监控系统资源使用

## 🔒 安全注意事项

1. **测试环境隔离**: 建议在测试环境运行，避免影响生产数据
2. **敏感信息保护**: 测试不会暴露真实的API密钥
3. **数据清理**: 及时清理测试产生的临时数据
4. **权限控制**: 确保测试脚本有适当的文件系统权限

## 📝 扩展测试

### 添加自定义测试

1. 在相应的测试类中添加新的测试方法
2. 在 `run_tests.php` 中注册新测试
3. 遵循现有的测试命名和结构规范

### 测试最佳实践

- 每个测试应该独立运行
- 测试后清理产生的数据
- 提供清晰的错误信息
- 包含性能基准检查

---

**作者**: Developer
**版本**: 1.0  
**更新时间**: 2025-05-31
