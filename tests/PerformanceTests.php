<?php
/**
 * 性能测试
 * 
 * 测试系统性能和资源使用情况
 * 
 * <AUTHOR>
 * @version 1.0
 */

require_once __DIR__ . '/../vendor/autoload.php';

use AlipayBillQuery\Database\Connection;
use AlipayBillQuery\Services\DataService;

class PerformanceTests
{
    /**
     * 测试数据库查询性能
     */
    public static function testDatabaseQueryPerformance()
    {
        echo "测试数据库查询性能...\n";
        
        try {
            $db = Connection::getInstance();
            
            // 测试简单查询性能
            $startTime = microtime(true);
            $result = $db->query("SELECT COUNT(*) as count FROM `25_zfb_data`")->fetch();
            $endTime = microtime(true);
            
            $queryTime = round(($endTime - $startTime) * 1000, 2);
            echo "✅ 简单查询耗时: {$queryTime}ms\n";
            
            if ($queryTime > 1000) {
                echo "⚠️  查询时间较长，可能需要优化\n";
            }
            
            // 测试复杂查询性能
            $startTime = microtime(true);
            $sql = "SELECT DATE(gmt_create) as date, COUNT(*) as count, SUM(total_amount) as total 
                    FROM `25_zfb_data` 
                    WHERE gmt_create >= DATE_SUB(NOW(), INTERVAL 30 DAY) 
                    GROUP BY DATE(gmt_create) 
                    ORDER BY date DESC 
                    LIMIT 10";
            $results = $db->query($sql)->fetchAll();
            $endTime = microtime(true);
            
            $complexQueryTime = round(($endTime - $startTime) * 1000, 2);
            echo "✅ 复杂查询耗时: {$complexQueryTime}ms\n";
            
            if ($complexQueryTime > 5000) {
                echo "⚠️  复杂查询时间较长，建议添加索引\n";
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 数据库查询性能测试异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试内存使用情况
     */
    public static function testMemoryUsage()
    {
        echo "测试内存使用情况...\n";
        
        try {
            $startMemory = memory_get_usage(true);
            $startPeakMemory = memory_get_peak_usage(true);
            
            echo "✅ 初始内存使用: " . self::formatBytes($startMemory) . "\n";
            echo "✅ 初始峰值内存: " . self::formatBytes($startPeakMemory) . "\n";
            
            // 执行一些操作来测试内存使用
            $dataService = new DataService();
            $stats = $dataService->getDataStatistics();
            
            // 模拟处理大量数据
            $testData = [];
            for ($i = 0; $i < 1000; $i++) {
                $testData[] = [
                    'id' => $i,
                    'data' => str_repeat('test', 100),
                    'timestamp' => time()
                ];
            }
            
            $currentMemory = memory_get_usage(true);
            $peakMemory = memory_get_peak_usage(true);
            
            echo "✅ 当前内存使用: " . self::formatBytes($currentMemory) . "\n";
            echo "✅ 峰值内存使用: " . self::formatBytes($peakMemory) . "\n";
            
            $memoryIncrease = $currentMemory - $startMemory;
            echo "✅ 内存增长: " . self::formatBytes($memoryIncrease) . "\n";
            
            // 检查内存使用是否合理
            if ($peakMemory > 128 * 1024 * 1024) { // 128MB
                echo "⚠️  内存使用较高，可能需要优化\n";
            }
            
            // 清理测试数据
            unset($testData);
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 内存使用测试异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试批量数据处理性能
     */
    public static function testBatchProcessingPerformance()
    {
        echo "测试批量数据处理性能...\n";
        
        try {
            $db = Connection::getInstance();
            $batchSizes = [10, 50, 100, 500];
            
            foreach ($batchSizes as $batchSize) {
                $startTime = microtime(true);
                
                // 准备测试数据
                $testData = [];
                for ($i = 0; $i < $batchSize; $i++) {
                    $testData[] = [
                        'alipay_order_no' => 'BATCH_TEST_' . time() . '_' . $i,
                        'gmt_create' => date('Y-m-d H:i:s'),
                        'goods_title' => "批量测试商品_{$i}",
                        'total_amount' => round(rand(1, 1000) / 100, 2),
                        'trade_status' => 'TRADE_SUCCESS',
                        'data_source' => 'batch_test'
                    ];
                }
                
                // 执行批量插入
                $db->beginTransaction();
                try {
                    foreach ($testData as $data) {
                        $sql = "INSERT INTO `25_zfb_data` 
                                (`alipay_order_no`, `gmt_create`, `goods_title`, `total_amount`, `trade_status`, `data_source`) 
                                VALUES (?, ?, ?, ?, ?, ?)";
                        $db->insert($sql, [
                            $data['alipay_order_no'],
                            $data['gmt_create'],
                            $data['goods_title'],
                            $data['total_amount'],
                            $data['trade_status'],
                            $data['data_source']
                        ]);
                    }
                    $db->commit();
                } catch (Exception $e) {
                    $db->rollback();
                    throw $e;
                }
                
                $endTime = microtime(true);
                $batchTime = round(($endTime - $startTime) * 1000, 2);
                $avgTime = round($batchTime / $batchSize, 2);
                
                echo "✅ 批量大小 {$batchSize}: 总耗时 {$batchTime}ms, 平均 {$avgTime}ms/条\n";
                
                // 清理测试数据
                $sql = "DELETE FROM `25_zfb_data` WHERE `data_source` = 'batch_test'";
                $db->update($sql, []);
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 批量处理性能测试异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试并发处理能力
     */
    public static function testConcurrencyHandling()
    {
        echo "测试并发处理能力...\n";
        
        try {
            // 模拟并发数据库连接
            $connections = [];
            $maxConnections = 5;
            
            $startTime = microtime(true);
            
            for ($i = 0; $i < $maxConnections; $i++) {
                try {
                    $connections[] = Connection::getInstance();
                    echo "✅ 连接 {$i}: 创建成功\n";
                } catch (Exception $e) {
                    echo "❌ 连接 {$i}: 创建失败 - " . $e->getMessage() . "\n";
                    return false;
                }
            }
            
            $endTime = microtime(true);
            $connectionTime = round(($endTime - $startTime) * 1000, 2);
            
            echo "✅ {$maxConnections} 个并发连接创建耗时: {$connectionTime}ms\n";
            
            // 测试并发查询
            $startTime = microtime(true);
            
            foreach ($connections as $index => $db) {
                $result = $db->query("SELECT COUNT(*) as count FROM `25_zfb_data`")->fetch();
                echo "✅ 连接 {$index}: 查询成功，记录数 " . number_format($result['count']) . "\n";
            }
            
            $endTime = microtime(true);
            $queryTime = round(($endTime - $startTime) * 1000, 2);
            
            echo "✅ 并发查询总耗时: {$queryTime}ms\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 并发处理测试异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 测试文件I/O性能
     */
    public static function testFileIOPerformance()
    {
        echo "测试文件I/O性能...\n";
        
        try {
            $testFile = __DIR__ . '/../logs/performance_test_' . time() . '.log';
            $testData = str_repeat("测试数据行\n", 1000);
            
            // 测试写入性能
            $startTime = microtime(true);
            file_put_contents($testFile, $testData);
            $endTime = microtime(true);
            
            $writeTime = round(($endTime - $startTime) * 1000, 2);
            $fileSize = filesize($testFile);
            
            echo "✅ 文件写入耗时: {$writeTime}ms\n";
            echo "✅ 文件大小: " . self::formatBytes($fileSize) . "\n";
            
            // 测试读取性能
            $startTime = microtime(true);
            $readData = file_get_contents($testFile);
            $endTime = microtime(true);
            
            $readTime = round(($endTime - $startTime) * 1000, 2);
            
            echo "✅ 文件读取耗时: {$readTime}ms\n";
            
            // 测试追加写入性能
            $appendData = "追加数据\n";
            $startTime = microtime(true);
            for ($i = 0; $i < 100; $i++) {
                file_put_contents($testFile, $appendData, FILE_APPEND | LOCK_EX);
            }
            $endTime = microtime(true);
            
            $appendTime = round(($endTime - $startTime) * 1000, 2);
            echo "✅ 100次追加写入耗时: {$appendTime}ms\n";
            
            // 清理测试文件
            if (file_exists($testFile)) {
                unlink($testFile);
                echo "✅ 测试文件清理完成\n";
            }
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 文件I/O性能测试异常: " . $e->getMessage() . "\n";
            return false;
        }
    }

    /**
     * 格式化字节数
     */
    private static function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * 测试系统资源使用
     */
    public static function testSystemResources()
    {
        echo "检查系统资源使用...\n";
        
        try {
            // 检查磁盘空间
            $diskFree = disk_free_space(__DIR__ . '/../');
            $diskTotal = disk_total_space(__DIR__ . '/../');
            $diskUsed = $diskTotal - $diskFree;
            $diskUsagePercent = round(($diskUsed / $diskTotal) * 100, 2);
            
            echo "✅ 磁盘使用: " . self::formatBytes($diskUsed) . " / " . self::formatBytes($diskTotal) . " ({$diskUsagePercent}%)\n";
            
            if ($diskUsagePercent > 90) {
                echo "⚠️  磁盘空间不足\n";
            }
            
            // 检查PHP配置
            $memoryLimit = ini_get('memory_limit');
            $maxExecutionTime = ini_get('max_execution_time');
            $uploadMaxFilesize = ini_get('upload_max_filesize');
            
            echo "✅ PHP内存限制: {$memoryLimit}\n";
            echo "✅ 最大执行时间: {$maxExecutionTime}秒\n";
            echo "✅ 最大上传文件: {$uploadMaxFilesize}\n";
            
            return true;
            
        } catch (Exception $e) {
            echo "❌ 系统资源检查异常: " . $e->getMessage() . "\n";
            return false;
        }
    }
}
