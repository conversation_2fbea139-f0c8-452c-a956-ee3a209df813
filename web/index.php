<?php
/**
 * 支付宝数据同步系统 Web 管理界面 - 合并版本
 * 包含管理首页和数据查看功能
 *
 * @version 2.0
 */

// 设置时区
require_once '../config/timezone.php';

require_once '../vendor/autoload.php';

use AlipayBillQuery\Services\DataService;
use AlipayBillQuery\Services\Scheduler;
use AlipayBillQuery\Database\Migration;
use AlipayBillQuery\Database\Connection;
use AlipayBillQuery\Models\ZfbData;

// 支付宝配置信息
$appId = '2021005119698187';
$privateKey = 'MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQCIAuPG6ks9kh07vX9fHTH/+6jNG1glO5/mqIXQKO/g3Dvu+eYFbket9vZbVAO0TQYd/p2Lzp2WZBmfxocHYDumM/cLvVAEW8sDKkTLOWgV06yvucnbalTltHus43KTcx1KvmybN2wJRxOsaRWAEk+awVkibhpKGliNr4b1ah8XYWCrePGAUUXMRj04vXufhGnSe+E6ryUGwul9ZjdYtLx4mznEHLNvPc7Vojv2R514Jwp1b/q3IqPSIArpaGSeUs79i/R1blC1LrSViniVqnLpp0kRkEuS0/hjDPImEcie0QwjWWfHKQqP30dTTh+gSh7v0Iv+yfOOi/PvcBncKZx1AgMBAAECggEALFAEtgIPkXfRXm1W2j5A1A3B6VFHXpoWdqfhMPilbrVSMYHpl0tevyb/DLJKoquVmqAh5DLk1OK4Fn4v8A9CX9v+WSzMrR7a/aT/1NZXOwVD9dyqD3qNPmmXAbT412Fh4cA40jk0UbF+j2WNQ7SzitADolwM5KfAwii1568zggISOoQdkvR9+ERdW0NbY+6nyQj5fPJeQb+PaSU4XUL+YvH38PBFQ21LSdxC4hpR+btYxu8KJ0a5lhXu8FPVEjshYMhUwwGUUXbt/7mer2Yg6X3zN2q6DUXvQXZAtLSUlzQvxDgIyUJAt61v+T3/xaZpRVLRFaSGsNOXUmRluZAQ4QKBgQDxkfJBYMnzg86WMZqFrToB4PwL8RgKFNUkL9LCHLVxK7kfksuYVJsr8BWvc8rrQuCjhjdBhylddUqeGDJ+8I36ti9UZ4vDgb0S1pMYvQRmUZeC37EKTKUjSsu5hICd8mgY3AuRUNJkFAkuFQMSeHSWe0dz2UyBVoM+oYJ1T8k3UwKBgQCQIsEA/dZYXVlfGGSVXhNmy8uTnCC010pQ2jq3rKiK0kxXvGSAooSIuML46hwn2TpJXW5sYPnvmMl4osEBtaGuP8OSlgJ/7ZP56LHCJeY2nBqOZzngyFdZwQBCkig4Dm1HZF53lfRQCks3YHauWMXpYa/dDn+yOgVZcOSgEJNMFwKBgQCDnr2cGZxvbhWViBllVGkStP8fkpFCjO9E9DmlQfcqXmRTa6w6p36Uhg+KtVCOtrWm424f6gEDxvCNCyoYOAFj5PgMyQ5By+K07Ozgwbwv86zVxgO0VOZ1QD+YKTXa2UUWpm43Ew5PMQt/bDtsSO1dQHZCDNe+cOC5s05dlMdRuQKBgQCIsOzoy9IjKyQ+kxuQrA8qRctiyYYa+rF3y/4zgoK0ZIwSCJAnjfiy0MXW2e6pu9ETEpBOKAnft74Zsf/oZyBV6BLJSYpFWEIllxA9V0PkNlbZBfxVuKlebTKZ75JE1ym7suwD7SotXhXHBqyG25mVoxbtRXrEw1GfaPjo8889MQKBgQDeTTXt6cv4PRB9VN981cqmJJUJVMqpI8CDjpzeZE95G3aljeQcfWBSpD1koudYMaTeWxXWLqt4a+Yz8D4/AAf5tZeAYpm7jksaihR4NVjG9w66JKcCXYa69e5yDIx5+Wlq6QAUWQOyFdttbYYIotGmAmJtHlkkZ+VnXRodRsz/Bg==';
$alipayPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq+s69Posg9bkvSDge58uVtNaSY0msXaAe4LyJTfDp1mvQKlpeRq01ic+yLnGNoEByosOqC4PG+xMzcahU/+1QD4Lnt5Y9p1uzMozGmE2pE2TZcIoaquW75ylxSYURGYHAJ2X5Xk9y1hVKJxLVeDCEY3HfCa+ymlWguSB8DYWE7mJyFtXWrSZOVzfiV6+m1FKVrfWsFBwf+Din8OKBes1AGMZ2xsVoCl+m4Mp9d1j8cbH0OwKTwNUJsOJLEKAxmy6Nkhl+6/fJu7tIAvQn76fi/oTYu71XWAfpoex1TehETBZ/6bNqSr3ztZLZF1fGGpcTr6gWan1Vye6VTzRApnvDwIDAQAB';

// 处理AJAX请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'];
        $response = ['success' => false, 'message' => '', 'data' => null];
        
        switch ($action) {
            case 'get_statistics':
                $dataService = new DataService();
                $stats = $dataService->getDataStatistics(true);
                $response = ['success' => true, 'data' => $stats];
                break;
                
            case 'get_sync_logs':
                $dataService = new DataService();
                $logs = $dataService->getSyncLogs(20, true);
                $response = ['success' => true, 'data' => $logs];
                break;

            case 'manual_sync':
                $dataService = new DataService();
                $success = $dataService->performIncrementalSync($appId, $privateKey, $alipayPublicKey, 60, true);
                $response = ['success' => $success, 'message' => $success ? '智能同步成功' : '同步失败'];
                break;

            case 'check_system':
                $dataService = new DataService();
                $status = $dataService->checkSystemStatus(true);
                $response = ['success' => $status, 'message' => $status ? '系统正常' : '系统异常'];
                break;

            case 'add_manual_data':
                // 手工添加数据
                try {
                    $goods_title = isset($_POST['goods_title']) ? trim($_POST['goods_title']) : '';
                    $total_amount = isset($_POST['total_amount']) ? floatval($_POST['total_amount']) : 0;

                    if (empty($goods_title) || $total_amount <= 0) {
                        throw new Exception('商品标题和金额不能为空');
                    }

                    $db = Connection::getInstance();

                    // 生成唯一的支付宝订单号（手工数据标识）
                    $alipay_order_no = 'MANUAL_' . date('YmdHis') . '_' . mt_rand(1000, 9999);

                    // 自动生成其他字段
                    $data = [
                        'alipay_order_no' => $alipay_order_no,
                        'merchant_order_no' => 'M_' . date('YmdHis') . '_' . mt_rand(100, 999),
                        'gmt_create' => date('Y-m-d H:i:s'),
                        'gmt_payment' => date('Y-m-d H:i:s'),
                        'goods_title' => $goods_title,
                        'total_amount' => $total_amount,
                        'other_account' => '手工录入',
                        'trade_status' => '成功',
                        'trade_type' => '手工录入',
                        'store_name' => '手工录入',
                        'trade_no' => 'T_' . date('YmdHis') . '_' . mt_rand(100, 999),
                        'subject' => $goods_title,
                        'receipt_amount' => $total_amount,
                        'buyer_pay_amount' => $total_amount,
                        'data_source' => 'manual'
                    ];

                    $zfbData = new ZfbData();
                    $result = $zfbData->insertOrUpdate($data);

                    $response = ['success' => true, 'message' => '手工数据添加成功', 'data' => $result];
                } catch (Exception $e) {
                    $response = ['success' => false, 'message' => $e->getMessage()];
                }
                break;

            case 'create_tables':
                $migration = new Migration();
                $success = $migration->runMigrations();
                $response = ['success' => $success, 'message' => $success ? '表创建成功' : '表创建失败'];
                break;

            case 'get_data':
                // 数据查看功能
                $page = isset($_POST['page']) ? max(1, intval($_POST['page'])) : 1;
                $limit = isset($_POST['limit']) ? max(10, min(100, intval($_POST['limit']))) : 20; // 支持动态limit，范围10-100
                $offset = ($page - 1) * $limit;

                $search = isset($_POST['search']) ? trim($_POST['search']) : '';
                $status = isset($_POST['status']) ? trim($_POST['status']) : '';
                $date_from = isset($_POST['date_from']) ? trim($_POST['date_from']) : '';
                $date_to = isset($_POST['date_to']) ? trim($_POST['date_to']) : '';

                try {
                    $db = Connection::getInstance();

                    // 构建查询条件
                    $where = ['1=1'];
                    $params = [];

                    if (!empty($search)) {
                        $where[] = "(goods_title LIKE ? OR alipay_order_no LIKE ? OR other_account LIKE ?)";
                        $params[] = "%{$search}%";
                        $params[] = "%{$search}%";
                        $params[] = "%{$search}%";
                    }

                    if (!empty($status)) {
                        $where[] = "trade_status = ?";
                        $params[] = $status;
                    }

                    if (!empty($date_from)) {
                        $where[] = "gmt_create >= ?";
                        $params[] = $date_from . ' 00:00:00';
                    }

                    if (!empty($date_to)) {
                        $where[] = "gmt_create <= ?";
                        $params[] = $date_to . ' 23:59:59';
                    }

                    $whereClause = implode(' AND ', $where);

                    // 获取总记录数
                    $countSql = "SELECT COUNT(*) as total FROM `25_zfb_data` WHERE {$whereClause}";
                    $totalResult = $db->query($countSql, $params)->fetch();
                    $total = $totalResult['total'];
                    $totalPages = ceil($total / $limit);

                    // 获取数据
                    $sql = "SELECT * FROM `25_zfb_data` WHERE {$whereClause} ORDER BY gmt_create DESC LIMIT {$limit} OFFSET {$offset}";
                    $stmt = $db->query($sql, $params);
                    $data = $stmt->fetchAll();

                    $response = [
                        'success' => true,
                        'data' => $data,
                        'total' => $total,
                        'page' => $page,
                        'totalPages' => $totalPages,
                        'limit' => $limit
                    ];
                } catch (Exception $e) {
                    $response = ['success' => false, 'message' => $e->getMessage()];
                }
                break;

            case 'get_status_list':
                // 获取交易状态列表
                try {
                    $db = Connection::getInstance();
                    $statusSql = "SELECT DISTINCT trade_status FROM `25_zfb_data` WHERE trade_status IS NOT NULL ORDER BY trade_status";
                    $statusList = $db->query($statusSql)->fetchAll();
                    $response = ['success' => true, 'data' => $statusList];
                } catch (Exception $e) {
                    $response = ['success' => false, 'message' => $e->getMessage()];
                }
                break;

            default:
                $response = ['success' => false, 'message' => '未知操作'];
        }
        
        echo json_encode($response);
        exit;
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付宝数据同步系统 - 管理界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f6fa;
            min-height: 100vh;
            color: #1f2937;
            line-height: 1.5;
        }

        .layout {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏 */
        .sidebar {
            width: 240px;
            background: #ffffff;
            border-right: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        .brand-name {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }

        .nav-menu {
            flex: 1;
            padding: 20px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #6b7280;
            text-decoration: none;
            transition: all 0.2s;
            border-left: 3px solid transparent;
        }

        .nav-item:hover {
            background: #f9fafb;
            color: #3b82f6;
        }

        .nav-item.active {
            background: #eff6ff;
            color: #3b82f6;
            border-left-color: #3b82f6;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 主内容区域 */
        .main-container {
            flex: 1;
            margin-left: 240px;
            display: flex;
            flex-direction: column;
        }

        .top-header {
            background: #ffffff;
            border-bottom: 1px solid #e5e7eb;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .page-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .main-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
        }

        .section {
            margin-bottom: 32px;
        }

        .section-header {
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 4px;
        }

        .section-subtitle {
            font-size: 14px;
            color: #6b7280;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 32px;
        }

        .card {
            background: #ffffff;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
            position: relative;
        }

        .card:hover {
            border-color: #d1d5db;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .card-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #eff6ff;
            border-radius: 6px;
            color: #3b82f6;
            font-size: 12px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
        }

        .stat-item:not(:last-child) {
            border-bottom: 1px solid #f3f4f6;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
        }

        .stat-value {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 12px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 10px 16px;
            border-radius: 8px;
            border: 1px solid transparent;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            white-space: nowrap;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
        }

        .btn-success {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }

        .btn-success:hover {
            background: #059669;
            border-color: #059669;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
            border-color: #f59e0b;
        }

        .btn-warning:hover {
            background: #d97706;
            border-color: #d97706;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
            border-color: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
            border-color: #4b5563;
        }

        .btn-outline {
            background: transparent;
            color: #6b7280;
            border-color: #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            color: #374151;
            border-color: #9ca3af;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .logs-container {
            background: white;
            border-radius: 16px;
            padding: 25px;
            max-height: 500px;
            overflow-y: auto;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid rgba(0,0,0,0.05);
        }
        
        .log-item {
            background: white;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        
        .log-item.error {
            border-left-color: #dc3545;
        }
        
        .log-item.running {
            border-left-color: #ffc107;
        }
        
        .log-time {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .log-content {
            color: #333;
        }
        
        .loading {
            text-align: center;
            padding: 30px;
            color: #666;
            font-size: 1.1em;
            position: relative;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
            vertical-align: middle;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: none;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .footer {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            text-align: center;
            color: #666;
            border-top: 1px solid rgba(0,0,0,0.05);
            font-weight: 500;
        }

        .footer::before {
            content: '✨';
            margin-right: 8px;
        }

        /* 筛选器样式 */
        .filters {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
        }

        .filter-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr auto;
            gap: 16px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 6px;
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .form-group input,
        .form-group select {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: #ffffff;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .data-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            gap: 20px;
        }

        .data-actions {
            display: flex;
            gap: 12px;
        }

        .stats {
            padding: 16px 20px;
            background: #eff6ff;
            color: #1d4ed8;
            font-weight: 500;
            border-radius: 8px;
            border: 1px solid #dbeafe;
            font-size: 14px;
            flex: 1;
        }

        /* 现代化数据展示 */
        .data-container {
            background: #ffffff;
            border: 1px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
        }

        .data-header {
            background: #f9fafb;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .data-title {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }

        .data-controls {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-size-selector label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }

        .page-size-selector select {
            padding: 6px 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            font-size: 14px;
            color: #374151;
            cursor: pointer;
        }

        .page-size-selector select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 表格视图 */
        .data-table {
            max-height: 600px;
            overflow-y: auto;
        }

        .modern-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .modern-table th {
            background: #f9fafb;
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            font-size: 13px;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
        }

        .modern-table td {
            padding: 16px 20px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: middle;
        }

        .modern-table tbody tr {
            transition: all 0.2s ease;
        }

        .modern-table tbody tr:hover {
            background: #f8fafc;
        }

        .modern-table tbody tr:last-child td {
            border-bottom: none;
        }

        .table-cell-amount {
            font-size: 16px;
            font-weight: 700;
            color: #059669;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
        }

        .table-cell-time {
            font-size: 13px;
            color: #6b7280;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            white-space: nowrap;
        }

        .table-cell-title {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: 500;
            color: #111827;
        }

        .table-cell-account {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            color: #6b7280;
        }

        .table-cell-order {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 13px;
            color: #6b7280;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-success {
            background: #d1fae5;
            color: #065f46;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }

        .pagination {
            padding: 20px;
            text-align: center;
            background: #ffffff;
            border-top: 1px solid #e5e7eb;
        }

        .pagination a,
        .pagination span {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 4px;
            border-radius: 6px;
            text-decoration: none;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            border: 1px solid #e5e7eb;
        }

        .pagination a:hover {
            background: #f3f4f6;
            border-color: #d1d5db;
        }

        .pagination .current {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .no-data {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }
        
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-container {
                margin-left: 0;
            }
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }

            .filter-row {
                grid-template-columns: 1fr;
                gap: 12px;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }

            .main-content {
                padding: 16px;
            }

            .card {
                padding: 16px;
            }

            .table-container {
                font-size: 13px;
            }

            th, td {
                padding: 8px 12px;
            }

            .top-header {
                padding: 12px 16px;
            }

            .page-title {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">💰</div>
                <div class="brand-name">支付宝数据同步</div>
            </div>

            <nav class="nav-menu">
                <a href="#" class="nav-item active" onclick="switchPage('overview', this)">
                    <div class="nav-icon">📊</div>
                    <span>数据概览</span>
                </a>
                <a href="#" class="nav-item" onclick="switchPage('realtime', this)">
                    <div class="nav-icon">📈</div>
                    <span>实时数据</span>
                </a>
                <a href="#" class="nav-item" onclick="switchPage('logs', this)">
                    <div class="nav-icon">📋</div>
                    <span>同步日志</span>
                </a>
                <a href="#" class="nav-item" onclick="switchPage('settings', this)">
                    <div class="nav-icon">⚙️</div>
                    <span>系统设置</span>
                </a>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-container">
            <!-- 顶部导航栏 -->
            <div class="top-header">
                <div class="page-title" id="page-title">数据概览</div>
                <div class="header-actions" id="header-actions">
                    <button class="btn btn-outline" onclick="refreshStats()">
                        <span>🔄</span>
                        刷新
                    </button>
                    <button class="btn btn-primary" onclick="manualSync(this)">
                        <span>⚡</span>
                        手动同步
                    </button>
                </div>
            </div>

            <!-- 主内容 -->
            <div class="main-content">
                <div class="alert alert-success" id="success-alert"></div>
                <div class="alert alert-error" id="error-alert"></div>

                <!-- 数据概览页面 -->
                <div class="page-content active" id="overview-page">
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">系统概览</div>
                            <div class="section-subtitle">查看系统运行状态和关键指标</div>
                        </div>

                        <div class="dashboard">
                            <div class="card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <div class="card-icon">📈</div>
                                        数据统计
                                    </div>
                                </div>
                                <div id="statistics-content">
                                    <div class="loading">正在加载统计信息</div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <div class="card-icon">🔄</div>
                                        系统状态
                                    </div>
                                </div>
                                <div id="system-status">
                                    <div class="loading">正在检查系统状态</div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <div class="card-title">
                                        <div class="card-icon">⚡</div>
                                        快速操作
                                    </div>
                                </div>
                                <div class="actions-grid">
                                    <button class="btn btn-success" onclick="manualSync(this)">
                                        <span>🔄</span>
                                        手动同步
                                    </button>
                                    <button class="btn btn-secondary" onclick="checkSystem()">
                                        <span>🔍</span>
                                        检查系统
                                    </button>
                                    <button class="btn btn-warning" onclick="refreshLogs()">
                                        <span>📋</span>
                                        刷新日志
                                    </button>
                                    <button class="btn btn-outline" onclick="refreshStats()">
                                        <span>📊</span>
                                        刷新统计
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- 实时数据页面 -->
                <div class="page-content" id="realtime-page">
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">实时数据</div>
                            <div class="section-subtitle">查看和筛选交易数据，支持实时搜索和导出</div>
                        </div>

                        <div class="filters">
                            <div class="filter-row">
                                <div class="form-group">
                                    <label>搜索关键词</label>
                                    <input type="text" id="search-input" placeholder="商品标题、订单号、账户...">
                                </div>

                                <div class="form-group">
                                    <label>交易状态</label>
                                    <select id="status-select">
                                        <option value="">全部状态</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>开始日期</label>
                                    <input type="date" id="date-from">
                                </div>

                                <div class="form-group">
                                    <label>结束日期</label>
                                    <input type="date" id="date-to">
                                </div>

                                <div class="form-group">
                                    <button type="button" class="btn btn-primary" onclick="searchData()">
                                        <span>🔍</span>
                                        搜索
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="data-summary">
                            <div class="stats" id="data-stats">
                                📈 正在加载数据...
                            </div>
                            <div class="data-actions">
                                <button class="btn btn-outline" onclick="exportData()">
                                    <span>📥</span>
                                    导出数据
                                </button>
                                <button class="btn btn-outline" onclick="refreshData()">
                                    <span>🔄</span>
                                    刷新
                                </button>
                            </div>
                        </div>

                        <!-- 手工数据添加 -->
                        <div class="card" style="margin-bottom: 20px;">
                            <div class="card-header">
                                <div class="card-title">
                                    <div class="card-icon">✏️</div>
                                    手工添加数据
                                </div>
                            </div>
                            <div class="card-body">
                                <form id="manual-data-form" style="display: flex; gap: 15px; align-items: end;">
                                    <div style="flex: 1;">
                                        <label style="display: block; margin-bottom: 5px; font-weight: 500;">商品标题</label>
                                        <input type="text" id="manual-goods-title" placeholder="请输入商品标题"
                                               style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                                    </div>
                                    <div style="flex: 0 0 150px;">
                                        <label style="display: block; margin-bottom: 5px; font-weight: 500;">金额</label>
                                        <input type="number" id="manual-amount" placeholder="0.00" step="0.01" min="0"
                                               style="width: 100%; padding: 8px 12px; border: 1px solid #d1d5db; border-radius: 6px;">
                                    </div>
                                    <button type="submit" class="btn btn-primary" style="flex: 0 0 auto;">
                                        <span>➕</span>
                                        添加数据
                                    </button>
                                </form>
                            </div>
                        </div>

                        <div class="data-container" id="data-container">
                            <div class="data-header">
                                <div class="data-title">交易记录</div>
                                <div class="data-controls">
                                    <div class="page-size-selector">
                                        <label style="margin-right: 10px; font-weight: 500;">每页显示：</label>
                                        <select id="page-size-select" onchange="changePageSize()"
                                                style="padding: 6px 10px; border: 1px solid #d1d5db; border-radius: 6px; background: white;">
                                            <option value="10">10条/页</option>
                                            <option value="20" selected>20条/页</option>
                                            <option value="30">30条/页</option>
                                            <option value="50">50条/页</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div id="data-content">
                                <div class="loading">正在加载数据</div>
                            </div>
                        </div>

                        <div class="pagination" id="data-pagination"></div>
                    </div>
                </div>

                <!-- 同步日志页面 -->
                <div class="page-content" id="logs-page">
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">同步日志</div>
                            <div class="section-subtitle">查看系统同步历史记录和详细信息</div>
                        </div>

                        <div class="card">
                            <div class="logs-container" id="logs-container">
                                <div class="loading">正在加载同步日志</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 系统设置页面 -->
                <div class="page-content" id="settings-page">
                    <div class="section">
                        <div class="section-header">
                            <div class="section-title">系统设置</div>
                            <div class="section-subtitle">配置系统参数和同步选项</div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <div class="card-title">
                                    <div class="card-icon">⚙️</div>
                                    功能开发中
                                </div>
                            </div>
                            <div style="padding: 40px; text-align: center; color: #6b7280;">
                                <div style="font-size: 48px; margin-bottom: 16px;">🚧</div>
                                <div style="font-size: 18px; font-weight: 500; margin-bottom: 8px;">系统设置功能开发中</div>
                                <div>敬请期待更多配置选项</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



    </div>

    <script>
        // 全局变量
        let currentDataPage = 1;
        let currentDataFilters = {
            search: '',
            status: '',
            date_from: '',
            date_to: ''
        };
        let autoRefreshInterval;
        let currentPageSize = 20; // 默认每页20条
        let currentData = null; // 缓存当前数据

        // 页面切换功能
        function switchPage(pageName, navElement) {
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            navElement.classList.add('active');

            // 更新页面内容
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            document.getElementById(pageName + '-page').classList.add('active');

            // 更新页面标题和操作按钮
            updatePageHeader(pageName);

            // 根据页面加载相应数据
            loadPageData(pageName);
        }

        function updatePageHeader(pageName) {
            const pageTitle = document.getElementById('page-title');
            const headerActions = document.getElementById('header-actions');

            switch(pageName) {
                case 'overview':
                    pageTitle.textContent = '数据概览';
                    headerActions.innerHTML = `
                        <button class="btn btn-outline" onclick="refreshStats()">
                            <span>🔄</span>
                            刷新
                        </button>
                        <button class="btn btn-primary" onclick="manualSync(this)">
                            <span>⚡</span>
                            手动同步
                        </button>
                    `;
                    break;
                case 'realtime':
                    pageTitle.textContent = '实时数据';
                    headerActions.innerHTML = `
                        <button class="btn btn-outline" onclick="refreshData()">
                            <span>🔄</span>
                            刷新数据
                        </button>
                        <button class="btn btn-outline" onclick="exportData()">
                            <span>📥</span>
                            导出
                        </button>
                    `;
                    break;
                case 'logs':
                    pageTitle.textContent = '同步日志';
                    headerActions.innerHTML = `
                        <button class="btn btn-outline" onclick="refreshLogs()">
                            <span>🔄</span>
                            刷新日志
                        </button>
                        <button class="btn btn-primary" onclick="manualSync(this)">
                            <span>⚡</span>
                            手动同步
                        </button>
                    `;
                    break;
                case 'settings':
                    pageTitle.textContent = '系统设置';
                    headerActions.innerHTML = `
                        <button class="btn btn-outline" disabled>
                            <span>⚙️</span>
                            开发中
                        </button>
                    `;
                    break;
            }
        }

        function loadPageData(pageName) {
            switch(pageName) {
                case 'overview':
                    loadStatistics();
                    checkSystemStatus();
                    break;
                case 'realtime':
                    loadStatusList();
                    // 初始化筛选条件
                    setTimeout(() => {
                        currentDataFilters = {
                            search: '',
                            status: '',
                            date_from: '',
                            date_to: ''
                        };
                        loadData();
                    }, 300);
                    break;
                case 'logs':
                    loadSyncLogs();
                    break;
                case 'settings':
                    // 设置页面暂无数据加载
                    break;
            }
        }

        // 页面大小改变功能
        function changePageSize() {
            const select = document.getElementById('page-size-select');
            currentPageSize = parseInt(select.value);

            // 保存到localStorage
            localStorage.setItem('pageSize', currentPageSize);

            // 重置到第一页并重新加载数据
            currentDataPage = 1;
            loadData();
        }

        // 手工数据添加功能
        function addManualData(goodsTitle, amount) {
            const formData = new FormData();
            formData.append('action', 'add_manual_data');
            formData.append('goods_title', goodsTitle);
            formData.append('total_amount', amount);

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('手工数据添加成功！', 'success');
                    // 清空表单
                    document.getElementById('manual-goods-title').value = '';
                    document.getElementById('manual-amount').value = '';
                    // 刷新数据
                    loadData();
                } else {
                    showAlert('添加失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('添加手工数据失败:', error);
                showAlert('添加失败: ' + error.message, 'error');
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化...');

            // 初始化概览页面
            loadStatistics();
            loadSyncLogs();
            checkSystemStatus();
            loadStatusList();

            // 从localStorage恢复页面大小设置
            const savedPageSize = localStorage.getItem('pageSize');
            if (savedPageSize) {
                currentPageSize = parseInt(savedPageSize);
                const select = document.getElementById('page-size-select');
                if (select) {
                    select.value = currentPageSize;
                }
            }

            // 添加手工数据表单事件监听
            const manualForm = document.getElementById('manual-data-form');
            if (manualForm) {
                manualForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const goodsTitle = document.getElementById('manual-goods-title').value.trim();
                    const amount = parseFloat(document.getElementById('manual-amount').value);

                    if (!goodsTitle) {
                        showAlert('请输入商品标题', 'error');
                        return;
                    }
                    if (!amount || amount <= 0) {
                        showAlert('请输入有效的金额', 'error');
                        return;
                    }

                    addManualData(goodsTitle, amount);
                });
            }

            // 每30秒自动刷新统计和日志
            startAutoRefresh();

            console.log('初始化完成');
        });



        // 开始自动刷新
        function startAutoRefresh() {
            stopAutoRefresh();
            autoRefreshInterval = setInterval(function() {
                loadStatistics();
                loadSyncLogs();
            }, 30000);
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }
        
        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alertId = type === 'success' ? 'success-alert' : 'error-alert';
            const alertElement = document.getElementById(alertId);
            alertElement.textContent = message;
            alertElement.style.display = 'block';
            
            setTimeout(() => {
                alertElement.style.display = 'none';
            }, 5000);
        }
        
        // 发送AJAX请求
        function sendRequest(action, callback) {
            const formData = new FormData();
            formData.append('action', action);
            
            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (callback) callback(data);
            })
            .catch(error => {
                console.error('请求失败:', error);
                showAlert('请求失败: ' + error.message, 'error');
            });
        }
        
        // 加载统计信息
        function loadStatistics() {
            sendRequest('get_statistics', function(response) {
                if (response.success) {
                    const stats = response.data;
                    const html = `
                        <div class="stat-item">
                            <span class="stat-label">总记录数</span>
                            <span class="stat-value">${stats.total_records.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">今日新增</span>
                            <span class="stat-value">${stats.today_records.toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">成功交易</span>
                            <span class="stat-value">${stats.success_count.toLocaleString()} 笔</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">成功金额</span>
                            <span class="stat-value">¥${parseFloat(stats.success_amount).toLocaleString()}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">最新记录</span>
                            <span class="stat-value">${stats.latest_time || 'N/A'}</span>
                        </div>
                    `;
                    document.getElementById('statistics-content').innerHTML = html;
                } else {
                    document.getElementById('statistics-content').innerHTML = '<div class="loading">加载失败</div>';
                }
            });
        }
        
        // 加载同步日志
        function loadSyncLogs() {
            sendRequest('get_sync_logs', function(response) {
                if (response.success) {
                    const logs = response.data;
                    let html = '';
                    
                    logs.forEach(log => {
                        const statusClass = log.status === 'success' ? '' : (log.status === 'failed' ? 'error' : 'running');
                        const statusIcon = log.status === 'success' ? '✅' : (log.status === 'failed' ? '❌' : '🔄');
                        
                        html += `
                            <div class="log-item ${statusClass}">
                                <div class="log-time">${log.created_at}</div>
                                <div class="log-content">
                                    ${statusIcon} ${log.sync_type} - ${log.status}<br>
                                    时间范围: ${log.start_time} ~ ${log.end_time}<br>
                                    记录数: ${log.total_records} (新增: ${log.new_records}, 更新: ${log.updated_records}, 错误: ${log.error_records})<br>
                                    执行时间: ${log.execution_time} 秒
                                    ${log.error_message ? '<br>错误: ' + log.error_message : ''}
                                </div>
                            </div>
                        `;
                    });
                    
                    document.getElementById('logs-container').innerHTML = html || '<div class="loading">暂无日志</div>';
                } else {
                    document.getElementById('logs-container').innerHTML = '<div class="loading">加载失败</div>';
                }
            });
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            sendRequest('check_system', function(response) {
                const statusHtml = response.success ? 
                    '<div class="stat-item"><span class="stat-label">系统状态</span><span class="stat-value" style="color: #28a745;">✅ 正常</span></div>' :
                    '<div class="stat-item"><span class="stat-label">系统状态</span><span class="stat-value" style="color: #dc3545;">❌ 异常</span></div>';
                
                document.getElementById('system-status').innerHTML = statusHtml;
            });
        }
        
        // 手动同步
        function manualSync(btn) {
            if (!btn) {
                // 如果没有传入按钮，尝试查找
                btn = document.querySelector('button[onclick*="manualSync"]');
            }

            if (btn) {
                btn.disabled = true;
                btn.textContent = '🔄 同步中...';
            }

            sendRequest('manual_sync', function(response) {
                if (btn) {
                    btn.disabled = false;
                    btn.textContent = '🔄 手动同步';
                }

                if (response.success) {
                    showAlert('手动同步成功！', 'success');
                    loadStatistics();
                    loadSyncLogs();
                } else {
                    showAlert('手动同步失败: ' + response.message, 'error');
                }
            });
        }
        
        // 检查系统
        function checkSystem() {
            checkSystemStatus();
            showAlert('系统状态已刷新', 'success');
        }
        
        // 刷新日志
        function refreshLogs() {
            loadSyncLogs();
            showAlert('日志已刷新', 'success');
        }
        
        // 刷新统计
        function refreshStats() {
            loadStatistics();
            showAlert('统计信息已刷新', 'success');
        }

        // 加载状态列表
        function loadStatusList() {
            sendRequest('get_status_list', function(response) {
                if (response.success) {
                    const statusSelect = document.getElementById('status-select');
                    statusSelect.innerHTML = '<option value="">全部状态</option>';

                    response.data.forEach(status => {
                        const option = document.createElement('option');
                        option.value = status.trade_status;
                        option.textContent = status.trade_status;
                        statusSelect.appendChild(option);
                    });
                }
            });
        }

        // 搜索数据
        function searchData() {
            currentDataPage = 1;
            currentDataFilters = {
                search: document.getElementById('search-input').value,
                status: document.getElementById('status-select').value,
                date_from: document.getElementById('date-from').value,
                date_to: document.getElementById('date-to').value
            };
            loadData();
        }

        // 加载数据
        function loadData(page = 1) {
            console.log('loadData called with page:', page);
            console.log('currentDataFilters:', currentDataFilters);

            currentDataPage = page;

            // 显示加载状态
            document.getElementById('data-content').innerHTML = '<div class="loading">正在加载数据...</div>';
            document.getElementById('data-stats').innerHTML = '📈 正在加载数据...';
            document.getElementById('data-pagination').innerHTML = '';

            const formData = new FormData();
            formData.append('action', 'get_data');
            formData.append('page', page);
            formData.append('limit', currentPageSize);
            formData.append('search', currentDataFilters.search || '');
            formData.append('status', currentDataFilters.status || '');
            formData.append('date_from', currentDataFilters.date_from || '');
            formData.append('date_to', currentDataFilters.date_to || '');

            console.log('Sending request with data:', {
                action: 'get_data',
                page: page,
                limit: currentPageSize,
                search: currentDataFilters.search || '',
                status: currentDataFilters.status || '',
                date_from: currentDataFilters.date_from || '',
                date_to: currentDataFilters.date_to || ''
            });

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    renderData(data);
                    renderPagination(data);
                    updateDataStats(data);
                } else {
                    let errorMessage = data.message || '未知错误';
                    let errorHtml = '<div class="no-data"><h3>😔 加载失败</h3><p>' + errorMessage + '</p>';

                    // 如果是表不存在的错误，提供创建表的按钮
                    if (errorMessage.includes('Table') || errorMessage.includes('table') || errorMessage.includes('不存在')) {
                        errorHtml += '<div style="margin-top: 20px;"><button class="btn btn-primary" onclick="createTables()">🔧 创建数据表</button></div>';
                    }

                    errorHtml += '</div>';
                    document.getElementById('data-content').innerHTML = errorHtml;
                }
            })
            .catch(error => {
                console.error('加载数据失败:', error);
                document.getElementById('data-content').innerHTML =
                    '<div class="no-data"><h3>😔 加载失败</h3><p>网络错误: ' + error.message + '</p></div>';
            });
        }

        // 渲染数据
        function renderData(data) {
            console.log('renderData called with data:', data);
            currentData = data; // 缓存数据

            const container = document.getElementById('data-content');
            if (!container) {
                console.error('data-content not found');
                return;
            }

            if (!data.data || data.data.length === 0) {
                container.innerHTML = '<div class="no-data"><h3>😔 暂无数据</h3><p>没有找到符合条件的交易记录</p></div>';
                return;
            }

            // 只使用表格视图
            renderTableView(data, container);
        }



        // 渲染表格视图
        function renderTableView(data, container) {
            let html = `
                <div class="data-table">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>交易时间</th>
                                <th>商品标题</th>
                                <th>金额</th>
                                <th>对方账户</th>
                                <th>状态</th>
                                <th>支付宝订单号</th>
                                <th>商户订单号</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            data.data.forEach(row => {
                const gmt_create = (row.gmt_create || '-').toString();
                const goods_title = (row.goods_title || '-').toString();
                const total_amount = parseFloat(row.total_amount || 0);
                const other_account = (row.other_account || '-').toString();
                const trade_status = (row.trade_status || '-').toString();
                const alipay_order_no = (row.alipay_order_no || '-').toString();
                const merchant_order_no = (row.merchant_order_no || '-').toString();

                // 状态样式
                let statusClass = 'status-pending';
                if (trade_status.includes('成功') || trade_status.includes('TRADE_SUCCESS')) {
                    statusClass = 'status-success';
                } else if (trade_status.includes('失败') || trade_status.includes('TRADE_CLOSED')) {
                    statusClass = 'status-failed';
                }

                html += `
                    <tr>
                        <td class="table-cell-time">${gmt_create}</td>
                        <td class="table-cell-title" title="${goods_title}">${goods_title}</td>
                        <td class="table-cell-amount">¥${total_amount.toLocaleString('zh-CN', {minimumFractionDigits: 2})}</td>
                        <td class="table-cell-account" title="${other_account}">${other_account}</td>
                        <td><span class="status ${statusClass}">${trade_status}</span></td>
                        <td class="table-cell-order" title="${alipay_order_no}">${alipay_order_no}</td>
                        <td class="table-cell-order" title="${merchant_order_no}">${merchant_order_no}</td>
                    </tr>
                `;
            });

            html += '</tbody></table></div>';
            container.innerHTML = html;
        }

        // 渲染分页
        function renderPagination(data) {
            if (data.totalPages <= 1) {
                document.getElementById('data-pagination').innerHTML = '';
                return;
            }

            let html = '';

            // 上一页
            if (data.page > 1) {
                html += `<span onclick="loadData(${data.page - 1})">« 上一页</span>`;
            }

            // 页码
            const start = Math.max(1, data.page - 2);
            const end = Math.min(data.totalPages, data.page + 2);

            for (let i = start; i <= end; i++) {
                if (i === data.page) {
                    html += `<span class="current">${i}</span>`;
                } else {
                    html += `<span onclick="loadData(${i})">${i}</span>`;
                }
            }

            // 下一页
            if (data.page < data.totalPages) {
                html += `<span onclick="loadData(${data.page + 1})">下一页 »</span>`;
            }

            document.getElementById('data-pagination').innerHTML = html;
        }

        // 更新数据统计
        function updateDataStats(data) {
            const statsText = `📈 共找到 ${data.total.toLocaleString()} 条记录，当前第 ${data.page}/${data.totalPages} 页`;
            document.getElementById('data-stats').innerHTML = statsText;
        }

        // 刷新数据
        function refreshData() {
            loadData(currentDataPage);
            showAlert('数据已刷新', 'success');
        }

        // 导出数据
        function exportData() {
            showAlert('导出功能开发中...', 'success');
        }

        // 创建数据表
        function createTables() {
            if (!confirm('确定要创建数据表吗？')) {
                return;
            }

            const formData = new FormData();
            formData.append('action', 'create_tables');

            // 显示加载状态
            document.getElementById('data-content').innerHTML = '<div class="loading">正在创建数据表...</div>';

            fetch('', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('数据表创建成功！', 'success');
                    // 重新加载数据
                    setTimeout(() => {
                        loadData();
                    }, 1000);
                } else {
                    showAlert('数据表创建失败：' + (data.message || '未知错误'), 'error');
                    document.getElementById('data-content').innerHTML =
                        '<div class="no-data"><h3>😔 创建失败</h3><p>' + (data.message || '未知错误') + '</p></div>';
                }
            })
            .catch(error => {
                console.error('创建表失败:', error);
                showAlert('创建表失败：' + error.message, 'error');
            });
        }


    </script>
</body>
</html>
